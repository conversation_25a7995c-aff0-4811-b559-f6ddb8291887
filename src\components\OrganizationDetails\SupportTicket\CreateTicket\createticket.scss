// CreateTicket specific styles to avoid conflicts
.support-ticket-section-wrapper .support-ticket-wrap,
.support-ticket-wrap {
  background-color: var(--color-white);
  border-radius: 0 0 var(--border-radius-md) var(--border-radius-md);
  min-height: fit-content;

  .support-ticket-form {
    // Form content wrapper - similar to support-ticket-dashboard in dashboard
    width: 100%;
  }

  .MuiInputBase-root {
    min-height: 37px !important;

    .MuiInputBase-input {
      padding: var(--field-padding) !important;
    }
  }

  // New layout classes for Subject, Category, and Issue Type row
  .ticket-form-first-row {
    display: flex;
    gap: var(--spacing-lg);
    flex-direction: row;
    align-items: flex-start;

    @media (max-width: 991px) {
      flex-direction: column;
    }
    .subject-field-wrap {
      flex: 1 1 50%;
      width: 50%;
      min-width: 0;

      @media (max-width: 991px) {
        flex: 1 1 100%;
        width: 100%;
      }
    }

    .category-issue-fields-wrap {
      flex: 1 1 50%;
      width: 50%;
      display: flex;
      gap: var(--spacing-lg);
      flex-direction: row;
      min-width: 0;

      @media (max-width: 991px) {
        flex: 1 1 100%;
        width: 100%;
        flex-direction: column;
      }

      .category-field-wrap,
      .issue-type-field-wrap {
        flex: 1 1 50%;
        width: 50%;
        min-width: 0;

        @media (max-width: 991px) {
          flex: 1 1 100%;
          width: 100%;
        }
      }
    }
  }

  .ticket-form-second-row {
    display: flex;
    gap: var(--spacing-lg);
    margin-top: var(--spacing-lg);

    @media (max-width: 991px) {
      flex-direction: column;
    }

    &.mobile-layout {
      flex-direction: column;
    }

    .priority-field-wrap {
      width: 25%;

      @media (max-width: 991px) {
        width: 100%;
      }
    }
  }

  .personal-info-wrap {
    margin-top: var(--spacing-lg);

    .title-text {
      padding-top: var(--spacing-lg);
    }
  }

  // Description Single Row Layout
  .description-single-row {
    margin-top: var(--spacing-lg);
    width: 50%;

    .description-field-wrap {
      width: 100%;
      min-width: 0;

      .MuiBox-root {
        width: 100%;
      }
    }
  }

  // Attachment Single Row Layout
  .attachment-single-row {
    margin-top: var(--spacing-lg);
    width: 50%;

    .attachment-field-wrap {
      width: 100%;
      min-width: 0;
      display: flex;
      flex-direction: column;

      // Ensure CustomImageUploader takes full width of its container
      .dropzone-media-container,
      .dropzone-media-preview-container {
        width: 100%;
        max-width: 100%;
      }

      // Override any existing width constraints on upload container
      .ticket-upload-container {
        width: 100%;
        max-width: 100%;
      }

      .ticket-preview-container {
        width: 100%;
        max-width: 100%;

        img {
          width: 100%;
          max-width: 100%;
          max-height: 112px;
          object-fit: cover;
        }

        .cancel-icon {
          top: -8px;
          right: -10px;
          fill: var(--icon-bold-red-color);
        }
      }
    }
  }

  // Personal information 3-column grid layout
  .personal-info-grid-container {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    column-gap: var(--spacing-lg);
    row-gap: var(--spacing-lg);
    width: 100%;

    // Force single column below 991px regardless of mobile-layout class
    @media (max-width: 991px) {
      grid-template-columns: 1fr;
    }

    @media (max-width: 767px) {
      grid-template-columns: 1fr;
    }

    &.mobile-layout {
      // This will only apply above 991px when user explicitly sets mobile layout
      @media (max-width: 991px) {
        grid-template-columns: 1fr;
      }
      // Below 991px, media query takes precedence
    }

    > div {
      width: 100%;
    }
  }

  // Pin fields grid layout - Same width as personal info fields (3 columns)
  .pin-fields-grid-container {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    column-gap: var(--spacing-lg);
    row-gap: var(--spacing-lg);
    margin-top: var(--spacing-lg);
    width: 100%;
    max-width: 100%;

    // Force single column below 991px regardless of mobile-layout class
    @media (max-width: 991px) {
      grid-template-columns: 1fr;
    }

    @media (max-width: 767px) {
      grid-template-columns: 1fr;
    }

    &.mobile-layout {
      // This will only apply above 991px when user explicitly sets mobile layout
      @media (max-width: 991px) {
        grid-template-columns: 1fr;
      }
      // Below 991px, media query takes precedence
    }

    > div {
      width: 100%;
    }
  }

  // Custom upload and preview container styles for ticket attachments
  .ticket-upload-container {
    width: 100%;
    max-width: 100%;
    min-height: 120px;
  }

  .ticket-preview-container {
    width: 100%;
    max-width: 100%;

    img {
      width: 100%;
      max-width: 100%;
      max-height: 150px;
      object-fit: cover;
      border-radius: var(--border-radius-md);
    }

    .cancel-icon {
      top: -8px;
      right: -10px;
      fill: var(--icon-bold-red-color);
    }
  }

  // New File Upload Zone Styles
  .ticket-upload-zone {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xxs);
    width: 100%;
  }

  .ticket-zone-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-xxs);
  }

  // Subject character counter styles (similar to attachment counter)
  .subject-header-with-counter {
    display: flex;
    justify-content: flex-end;
    margin-bottom: var(--spacing-xs);
  }

  .ticket-drop-area {
    position: relative;
    border: var(--border-width-sm) dashed var(--border-color-light-gray);
    border-radius: var(--border-radius-md);
    text-align: center;
    transition: all 0.15s ease-out;
    cursor: pointer;
    min-height: 123px;
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover {
      border-color: var(--color-primary);
      background-color: var(--color-off-white);
    }

    &--drag-over {
      border-color: var(--color-primary);
      background-color: var(--color-primary-opacity);
    }

    &--disabled {
      opacity: 0.5;
      pointer-events: none;
    }
  }

  .ticket-drop-input {
    position: absolute;
    inset: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    cursor: pointer;

    &:disabled {
      cursor: not-allowed;
    }
  }

  .ticket-drop-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-md);
    pointer-events: none;
  }

  .ticket-drop-icon {
    color: var(--color-primary);
  }

  .ticket-drop-text {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-tiny);
  }

  // File Preview Grid
  .ticket-preview-grid {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-lg);
    margin-top: var(--spacing-md);
  }

  .ticket-preview-item {
    position: relative;

    &:hover {
      .ticket-preview-remove {
        opacity: 1;
        background-color: var(--icon-bold-red-color);
      }
    }

    // Show remove button on mobile devices
    @media (max-width: 768px) {
      .ticket-preview-remove {
        opacity: 0.8;
      }
    }
  }

  .ticket-preview-container {
    width: 100px;
    height: 100px;
    background-color: var(--color-off-white);
    border-radius: var(--border-radius-md);
    overflow: hidden;
    border: var(--border-width-xs) solid var(--border-color-light-gray);
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .ticket-preview-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--color-light-gray);
  }

  .ticket-preview-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .ticket-preview-video {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .ticket-preview-video-element {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .ticket-preview-video-overlay {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: rgba(0, 0, 0, 0.6);
    border-radius: var(--border-radius-full);
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    pointer-events: none;
  }

  .ticket-preview-remove {
    position: absolute;
    top: calc(-1 * var(--spacing-sm));
    right: calc(-1 * var(--spacing-sm));
    background-color: var(--color-danger);
    color: var(--text-color-white);
    border-radius: var(--border-radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.15s ease-out;
    border: none;
    cursor: pointer;
    padding: var(--spacing-xs);
    box-shadow: var(--box-shadow-md);
    width: 24px;
    height: 24px;
    z-index: 10;

    &:hover {
      background-color: var(--icon-bold-red-color);
      opacity: 1 !important;
      transform: scale(1.1);
    }
  }

  .ticket-preview-info {
    margin-top: var(--spacing-sm);
    width: 100px;
    text-align: center;
    padding: 0 var(--spacing-xxs);
  }

  .ticket-preview-filename {
    display: block;
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-weight: 600;
    margin-bottom: 3px;
    color: var(--text-color-dark);
    font-size: 12px;
    line-height: 1.2;
  }

  .ticket-preview-filesize {
    color: var(--text-color-gray);
    font-size: 10px;
    font-weight: 500;
    line-height: 1.2;
  }

  // Error Display
  .ticket-upload-error {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    color: var(--color-danger);
    margin-top: var(--spacing-sm);
    padding: var(--spacing-sm);
    background-color: var(--color-danger-opacity);
    border-radius: var(--border-radius-sm);
  }

  .subject-label-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: var(--spacing-md);

    @media (max-width: 600px) {
      flex-direction: column;
      align-items: flex-start;
      gap: 0;
    }
  }
}

.attachment-label {
  color: var(--color-primary) !important;
  font-weight: 600;
  font-size: 16px;
  margin-bottom: var(--spacing-xs);
}

.ticket-drop-placeholder-primary {
  color: var(--color-primary) !important;
  font-weight: 600;
  font-size: 16px;
  text-align: center;
  margin-bottom: var(--spacing-xs);
}

// Media Upload Section Preview Styles - From Reference Design
.media-upload-section__preview-grid {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-lg);
  margin-top: var(--spacing-md);
}

.media-upload-section__preview-item {
  position: relative;

  &:hover .media-upload-section__preview-remove {
    opacity: 1;
  }
}

.media-upload-section__preview-container {
  width: 100px;
  height: 100px;
  background-color: var(--color-off-white);
  border-radius: var(--border-radius-md);
  overflow: hidden;
  border: var(--border-width-xs) solid var(--border-color-light-gray);
}

.media-upload-section__preview-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--color-light-gray);
}

.media-upload-section__preview-image {
  width: 100%;
  height: 100%;
  object-fit: scale-down;

  &.ticket-attachment-preview {
    pointer-events: none !important;
    cursor: default !important;
    user-select: none !important;
    -webkit-user-select: none !important;
    -moz-user-select: none !important;
    -ms-user-select: none !important;
  }
}

// Additional protection for ticket edit preview containers
.ticket-edit-preview-container {
  pointer-events: none !important;
  user-select: none !important;
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;

  * {
    pointer-events: none !important;
    user-select: none !important;
    -webkit-user-select: none !important;
    -moz-user-select: none !important;
    -ms-user-select: none !important;
    cursor: default !important;
  }

  // Prevent any click events on images
  img,
  [data-no-preview="true"],
  [data-no-click="true"] {
    pointer-events: none !important;
    user-select: none !important;
    cursor: default !important;
  }
}

.media-upload-section__preview-remove {
  position: absolute;
  top: calc(-1 * var(--spacing-sm));
  right: calc(-1 * var(--spacing-sm));
  background-color: var(--color-danger);
  color: var(--text-color-white);
  border-radius: var(--border-radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: all 0.15s ease-out;
  border: none;
  cursor: pointer;
  padding: var(--spacing-xxs);

  &:hover {
    background-color: var(--icon-bold-red-color);
    opacity: 1 !important;
    transform: scale(1.1);
  }
}

.media-upload-section__preview-info {
  margin-top: var(--spacing-sm);
}

.media-upload-section__preview-name {
  font-family: var(--font-family-primary);
  font-size: var(--font-size-xxs);
  color: var(--text-color-primary);
  width: 100px;
  word-break: break-word;
  margin: 0;
  padding: 0;
}

// Terms and Conditions Link Styles
.support-ticket-wrap {
  .terms-condition-label {
    font-size: 14px;
    color: var(--text-color-primary);

    .terms-link {
      color: var(--color-primary);
      font-weight: 500;
      padding-left: 5px;
      &:visited {
        color: var(--color-primary);
      }
    }
  }
}
