'use client';
import React, { useState } from 'react';
import FilterCollapse from '@/components/UI/FilterCollapse/FilterCollapse';
import { Box } from '@mui/material';
import CommonTable from '@/components/UI/CommonTable/CommonTable';
import { Chip, IconButton } from '@mui/material';
import MoreVertIcon from '@mui/icons-material/MoreVert';

const departmentOptions = [
  { label: 'Select Department', value: '' },
  { label: 'HR', value: 'hr' },
  { label: 'Finance', value: 'finance' },
  { label: 'Operations', value: 'operations' },
];

const userOptions = [
  { label: 'Select User', value: '' },
  { label: '<PERSON>', value: 'john' },
  { label: '<PERSON>', value: 'jane' },
];

const filterFields = [
  {
    type: 'date-range',
    label: 'Date Range',
    name: 'dateRange',
    placeholder: 'Select date range',
    format: 'MMM dd, yyyy',
  },
  {
    type: 'select',
    label: 'Department',
    name: 'department',
    options: departmentOptions,
    placeholder: 'Select Department',
  },
  {
    type: 'select',
    label: 'User',
    name: 'user',
    options: userOptions,
    placeholder: 'Select User',
  },
];

const statusColors = {
  Scheduled: 'default',
  Completed: 'info',
  Cancelled: 'error',
};

const columns = [
  { header: 'Employee', accessor: 'employee', sortable: true },
  { header: 'Department', accessor: 'department', sortable: true },
  { header: 'Shift', accessor: 'shift', sortable: true },
  { header: 'Date', accessor: 'date', sortable: true },
  { header: 'Hours', accessor: 'hours', sortable: false },
  {
    header: 'Status',
    accessor: 'status',
    sortable: true,
    renderCell: (value) => (
      <Chip
        label={value}
        color={statusColors[value] || 'default'}
        size="small"
        style={{ fontWeight: 600 }}
      />
    ),
  },
];

const staticData = [
  {
    employee: 'John Smith',
    department: 'Sales',
    shift: 'Morning',
    date: '2024-01-15',
    hours: '9:00 - 17:00',
    status: 'Scheduled',
  },
  {
    employee: 'Sarah Johnson',
    department: 'Marketing',
    shift: 'Evening',
    date: '2024-01-15',
    hours: '14:00 - 22:00',
    status: 'Completed',
  },
  {
    employee: 'Mike Wilson',
    department: 'IT',
    shift: 'Night',
    date: '2024-01-16',
    hours: '22:00 - 06:00',
    status: 'Scheduled',
  },
  {
    employee: 'Emma Davis',
    department: 'HR',
    shift: 'Morning',
    date: '2024-01-16',
    hours: '8:00 - 16:00',
    status: 'Cancelled',
  },
  {
    employee: 'David Lee',
    department: 'Finance',
    shift: 'Evening',
    date: '2024-01-17',
    hours: '14:00 - 22:00',
    status: 'Scheduled',
  },
  {
    employee: 'Olivia Brown',
    department: 'Operations',
    shift: 'Night',
    date: '2024-01-17',
    hours: '22:00 - 06:00',
    status: 'Completed',
  },
  {
    employee: 'James Miller',
    department: 'Sales',
    shift: 'Morning',
    date: '2024-01-18',
    hours: '9:00 - 17:00',
    status: 'Scheduled',
  },
  {
    employee: 'Sophia Wilson',
    department: 'Marketing',
    shift: 'Evening',
    date: '2024-01-18',
    hours: '14:00 - 22:00',
    status: 'Cancelled',
  },
  {
    employee: 'Benjamin Clark',
    department: 'IT',
    shift: 'Night',
    date: '2024-01-19',
    hours: '22:00 - 06:00',
    status: 'Scheduled',
  },
  {
    employee: 'Ava Martinez',
    department: 'HR',
    shift: 'Morning',
    date: '2024-01-19',
    hours: '8:00 - 16:00',
    status: 'Completed',
  },
  {
    employee: 'William Harris',
    department: 'Finance',
    shift: 'Evening',
    date: '2024-01-20',
    hours: '14:00 - 22:00',
    status: 'Scheduled',
  },
  {
    employee: 'Mia Lewis',
    department: 'Operations',
    shift: 'Night',
    date: '2024-01-20',
    hours: '22:00 - 06:00',
    status: 'Scheduled',
  },
  {
    employee: 'Elijah Walker',
    department: 'Sales',
    shift: 'Morning',
    date: '2024-01-21',
    hours: '9:00 - 17:00',
    status: 'Completed',
  },
  {
    employee: 'Charlotte Hall',
    department: 'Marketing',
    shift: 'Evening',
    date: '2024-01-21',
    hours: '14:00 - 22:00',
    status: 'Scheduled',
  },
  {
    employee: 'Lucas Allen',
    department: 'IT',
    shift: 'Night',
    date: '2024-01-22',
    hours: '22:00 - 06:00',
    status: 'Cancelled',
  },
  {
    employee: 'Amelia Young',
    department: 'HR',
    shift: 'Morning',
    date: '2024-01-22',
    hours: '8:00 - 16:00',
    status: 'Scheduled',
  },
  {
    employee: 'Henry King',
    department: 'Finance',
    shift: 'Evening',
    date: '2024-01-23',
    hours: '14:00 - 22:00',
    status: 'Completed',
  },
  {
    employee: 'Emily Wright',
    department: 'Operations',
    shift: 'Night',
    date: '2024-01-23',
    hours: '22:00 - 06:00',
    status: 'Scheduled',
  },
  {
    employee: 'Jack Scott',
    department: 'Sales',
    shift: 'Morning',
    date: '2024-01-24',
    hours: '9:00 - 17:00',
    status: 'Scheduled',
  },
  {
    employee: 'Grace Green',
    department: 'Marketing',
    shift: 'Evening',
    date: '2024-01-24',
    hours: '14:00 - 22:00',
    status: 'Completed',
  },
  {
    employee: 'Daniel Adams',
    department: 'IT',
    shift: 'Night',
    date: '2024-01-25',
    hours: '22:00 - 06:00',
    status: 'Scheduled',
  },
  {
    employee: 'Ella Baker',
    department: 'HR',
    shift: 'Morning',
    date: '2024-01-25',
    hours: '8:00 - 16:00',
    status: 'Cancelled',
  },
  {
    employee: 'Matthew Nelson',
    department: 'Finance',
    shift: 'Evening',
    date: '2024-01-26',
    hours: '14:00 - 22:00',
    status: 'Scheduled',
  },
  {
    employee: 'Scarlett Carter',
    department: 'Operations',
    shift: 'Night',
    date: '2024-01-26',
    hours: '22:00 - 06:00',
    status: 'Completed',
  },
  {
    employee: 'Sebastian Perez',
    department: 'Sales',
    shift: 'Morning',
    date: '2024-01-27',
    hours: '9:00 - 17:00',
    status: 'Scheduled',
  },
  {
    employee: 'Chloe Roberts',
    department: 'Marketing',
    shift: 'Evening',
    date: '2024-01-27',
    hours: '14:00 - 22:00',
    status: 'Scheduled',
  },
  {
    employee: 'Alexander Turner',
    department: 'IT',
    shift: 'Night',
    date: '2024-01-28',
    hours: '22:00 - 06:00',
    status: 'Completed',
  },
  {
    employee: 'Lily Phillips',
    department: 'HR',
    shift: 'Morning',
    date: '2024-01-28',
    hours: '8:00 - 16:00',
    status: 'Scheduled',
  },
  {
    employee: 'Mason Campbell',
    department: 'Finance',
    shift: 'Evening',
    date: '2024-01-29',
    hours: '14:00 - 22:00',
    status: 'Cancelled',
  },
  {
    employee: 'Zoe Parker',
    department: 'Operations',
    shift: 'Night',
    date: '2024-01-29',
    hours: '22:00 - 06:00',
    status: 'Scheduled',
  },
];

const actions = () => (
  <IconButton size="small">
    <MoreVertIcon />
  </IconButton>
);

export default function RotaReports() {
  const [filters, setFilters] = useState({});

  const handleApplyFilters = (values) => {
    setFilters(values);
    // You can trigger your report fetch here
    // console.log('Applied Filters:', values);
  };

  return (
    <Box className="report-main-container">
      <FilterCollapse
        fields={filterFields}
        onApply={handleApplyFilters}
        buttonText="Apply Filters"
        initialValues={filters}
      />
      {/* Your report content goes here */}

      <Box className="report-table-container">
        <CommonTable
          columns={columns}
          data={staticData}
          pageSize={10}
          actions={actions}
        />
      </Box>
    </Box>
  );
}
