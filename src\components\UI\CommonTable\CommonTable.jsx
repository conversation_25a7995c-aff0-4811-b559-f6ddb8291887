import React, { useState } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Box,
} from '@mui/material';
import { ArrowUpward, ArrowDownward } from '@mui/icons-material';
import CustomOrgPagination from '../customPagination';
import './CommonTable.scss';

function descendingComparator(a, b, orderBy) {
  if (b[orderBy] < a[orderBy]) {
    return -1;
  }
  if (b[orderBy] > a[orderBy]) {
    return 1;
  }
  return 0;
}

function getComparator(order, orderBy) {
  return order === 'desc'
    ? (a, b) => descendingComparator(a, b, orderBy)
    : (a, b) => -descendingComparator(a, b, orderBy);
}

function stableSort(array, comparator) {
  const stabilizedThis = array.map((el, index) => [el, index]);
  stabilizedThis.sort((a, b) => {
    const order = comparator(a[0], b[0]);
    if (order !== 0) return order;
    return a[1] - b[1];
  });
  return stabilizedThis.map((el) => el[0]);
}

const CommonTable = ({
  columns,
  data,
  pageSize = 10,
  onRowClick,
  actions,
  paginationProps = {},
}) => {
  // External pagination support
  const [order, setOrder] = useState('asc');
  const [orderBy, setOrderBy] = useState(columns[0]?.accessor || '');
  const [page, setPage] = useState(1); // CustomOrgPagination is 1-based
  const [rowsPerPage, setRowsPerPage] = useState(pageSize);

  const handleRequestSort = (property) => {
    const isAsc = orderBy === property && order === 'asc';
    setOrder(isAsc ? 'desc' : 'asc');
    setOrderBy(property);
  };

  const handleChangePage = (newPage) => {
    setPage(newPage);
    if (paginationProps.onPageChange) paginationProps.onPageChange(newPage);
  };

  const handleRowsPerPageChange = (newRowsPerPage) => {
    setRowsPerPage(Number(newRowsPerPage));
    setPage(1);
    if (paginationProps.onRowsPerPageChange)
      paginationProps.onRowsPerPageChange(Number(newRowsPerPage));
  };

  const paginatedData = stableSort(data, getComparator(order, orderBy)).slice(
    (page - 1) * rowsPerPage,
    (page - 1) * rowsPerPage + rowsPerPage
  );

  return (
    <Paper className="common-table-container">
      <TableContainer>
        <Table size="small" aria-label="a dense table">
          <TableHead>
            <TableRow>
              {columns.map((col) => (
                <TableCell
                  key={col.accessor}
                  sortDirection={orderBy === col.accessor ? order : false}
                >
                  {col.sortable ? (
                    <Box
                      sx={{
                        display: 'flex',
                        alignItems: 'center',
                        cursor: 'pointer',
                        fontWeight: 'bold',
                      }}
                      onClick={() => handleRequestSort(col.accessor)}
                    >
                      {col.header}
                      {orderBy === col.accessor &&
                        (order === 'asc' ? (
                          <ArrowUpward sx={{ ml: 0.5, fontSize: '1rem' }} />
                        ) : (
                          <ArrowDownward sx={{ ml: 0.5, fontSize: '1rem' }} />
                        ))}
                    </Box>
                  ) : (
                    col.header
                  )}
                </TableCell>
              ))}
              {actions && <TableCell>Actions</TableCell>}
            </TableRow>
          </TableHead>
          <TableBody>
            {paginatedData.map((row, idx) => (
              <TableRow
                key={idx}
                hover
                className="common-table-row"
                onClick={onRowClick ? () => onRowClick(row) : undefined}
                style={{ cursor: onRowClick ? 'pointer' : 'default' }}
              >
                {columns.map((col) => (
                  <TableCell key={col.accessor}>
                    {col.renderCell
                      ? col.renderCell(row[col.accessor], row)
                      : row[col.accessor]}
                  </TableCell>
                ))}
                {actions && <TableCell>{actions(row)}</TableCell>}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
      <Box className="common-table-pagination">
        <CustomOrgPagination
          currentPage={page}
          totalCount={data.length}
          rowsPerPage={rowsPerPage}
          onPageChange={handleChangePage}
          OnRowPerPage={handleRowsPerPageChange}
          {...paginationProps}
        />
      </Box>
    </Paper>
  );
};

export default CommonTable;
