'use client';
import { useState, useEffect } from 'react';
// import { useDropzone } from 'react-dropzone';
import { Box, Typography } from '@mui/material';
// import CustomButton from '@/components/UI/CustomButton';
import { EmptAttachmentIcon } from '@/helper/common/images';
// import DeleteOutlineIcon from '@mui/icons-material/DeleteOutline';
import DialogBox from '@/components/UI/Modalbox';
import FileGrid from './FileGrid';
import ContentLoader from '@/components/UI/ContentLoader';
// import AuthContext from '@/helper/authcontext';
import './attachment.scss';

export default function Attachment({ attachments = [] }) {
  // const [mediaFiles, setMediaFiles] = useState([]);
  const [previewMedia, setPreviewMedia] = useState(null);
  const [openModal, setOpenModal] = useState(false);
  const [loading, setLoading] = useState(true);
  // const { planDetail, authState, setRestrictedLimitModal } =
  //   useContext(AuthContext);

  // Simulate loading for consistency with other tabs
  useEffect(() => {
    const timer = setTimeout(() => {
      setLoading(false);
    }, 500); // Brief loading to match other tabs

    return () => clearTimeout(timer);
  }, [attachments]);

  // Show API attachments if present and no local uploads
  const hasApiAttachments = attachments && attachments?.length > 0;

  // Convert API attachments to display format
  const apiAttachments = hasApiAttachments
    ? attachments?.map((att) => ({
        name:
          att?.attachment_name ||
          att?.item_name ||
          att?.name ||
          att?.file_name ||
          'Unknown File',
        preview:
          att?.download_url || att?.url || att?.file_url || att?.preview_url,
        downloadUrl: att?.download_url || att?.url || att?.file_url,
        type: (att?.mime_type || att?.item_mime_type || att?.type || '')?.split(
          '/'
        )?.[0],
        mimeType: att?.mime_type || att?.item_mime_type || att?.type,
        isApiAttachment: true, // Flag to identify API attachments
        originalData: att, // Keep original data for debugging
      }))
    : [];

  // Combine API attachments with local media files
  // const displayFiles = [...apiAttachments, ...mediaFiles];
  const displayFiles = [...apiAttachments];

  const handlePreviewClick = (media) => {
    setPreviewMedia(media);
    setOpenModal(true);
  };

  // const { getRootProps, getInputProps, open } = useDropzone({
  //   onDrop: (acceptedFiles) => {
  //     // Check if storage is full
  //     const totalStorage = planDetail?.total_storage || 0;
  //     const usedStorage = authState?.subscriptionUsage?.total_size_gb || 0;
  //     const totalNewFileSizeInGB = acceptedFiles?.reduce((total, file) => {
  //       return total + file?.size / (1024 * 1024 * 1024); // Convert bytes to GB
  //     }, 0);

  //     if (usedStorage + totalNewFileSizeInGB > totalStorage) {
  //       setRestrictedLimitModal({
  //         storageLimit: true,
  //         totalStorage: planDetail?.total_storage,
  //         usedStorage: authState?.subscriptionUsage?.total_size_gb,
  //       });
  //       return;
  //     }

  //     if (acceptedFiles?.length) {
  //       const newMedia = acceptedFiles?.map((file) => ({
  //         file,
  //         name: file?.name,
  //         preview: URL.createObjectURL(file),
  //         type: file?.type?.split('/')?.[0],
  //       }));
  //       setMediaFiles((prevFiles) => [...(prevFiles || []), ...newMedia]);
  //     }
  //   },
  //   accept: 'image/*,video/*',
  //   multiple: true,
  //   noClick: true,
  // });

  // const removeMedia = (index) => {
  //   setMediaFiles((prevFiles) => prevFiles?.filter((_, i) => i !== index));
  // };

  const handleDownload = async (fileUrl, fileName) => {
    try {
      // Check if the URL is valid
      if (!fileUrl || !fileName) {
        console.error('Invalid file URL or filename');
        return;
      }

      // Clean up the filename to ensure it's valid
      const cleanFileName = fileName.replace(/[^a-zA-Z0-9.-]/g, '_');

      // For external URLs or files that need to be fetched
      if (fileUrl.startsWith('http')) {
        try {
          // Try to fetch the file and create a blob
          const response = await fetch(fileUrl, {
            mode: 'cors',
            credentials: 'include',
          });

          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }

          const blob = await response.blob();
          const url = window.URL.createObjectURL(blob);

          // Create download link
          const link = document.createElement('a');
          link.href = url;
          link.download = cleanFileName;
          link.style.display = 'none';

          // Append to body, click, and cleanup
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);

          // Clean up the blob URL
          window.URL.revokeObjectURL(url);
        } catch {
          // If fetch fails due to CORS or other issues, try direct download
          const link = document.createElement('a');
          link.href = fileUrl;
          link.download = cleanFileName;
          link.target = '_blank';
          link.style.display = 'none';

          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
        }
      } else {
        // For local files or data URLs
        const link = document.createElement('a');
        link.href = fileUrl;
        link.download = cleanFileName;
        link.style.display = 'none';

        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      }
    } catch (error) {
      console.error('Download failed:', error);
      // Fallback: open in new tab if download fails
      window.open(fileUrl, '_blank');
    }
  };

  // Direct preview content based on media type
  const getPreviewContent = () => {
    if (!previewMedia) return null;

    // Handle both API attachments and local files
    const mediaUrl =
      previewMedia?.preview ||
      previewMedia?.downloadUrl ||
      previewMedia?.download_url ||
      previewMedia?.url ||
      previewMedia?.file_url ||
      previewMedia;
    const mediaType =
      previewMedia?.type || previewMedia?.mimeType?.split('/')?.[0];
    const mimeType = previewMedia?.mimeType || previewMedia?.item_mime_type;

    // Validate media URL
    if (!mediaUrl) {
      return (
        <div className="preview-content">
          <Box className="preview-default">
            <Typography className="body-sm">
              No preview URL available for this file.
            </Typography>
          </Box>
        </div>
      );
    }

    // Check if it's an image by file extension if MIME type is not available
    const isImageByExtension =
      mediaUrl && /\.(jpg|jpeg|png|gif|bmp|webp|svg)$/i.test(mediaUrl);
    const isVideoByExtension =
      mediaUrl && /\.(mp4|webm|ogg|avi|mov)$/i.test(mediaUrl);

    // Video files
    if (
      mediaType === 'video' ||
      mimeType?.startsWith('video/') ||
      isVideoByExtension
    ) {
      return (
        <div className="preview-content">
          <video
            controls
            className="preview-video"
            src={mediaUrl}
            onError={(e) => {
              console.error('Video preview failed:', e);
            }}
          />
        </div>
      );
    }

    // Image files
    if (
      mediaType === 'image' ||
      mimeType?.startsWith('image/') ||
      isImageByExtension
    ) {
      return (
        <div className="preview-content">
          <img
            src={mediaUrl}
            alt="Preview"
            className="preview-image"
            onError={(e) => {
              console.error('Image preview failed:', e);
              // Hide the broken image
              e.target.style.display = 'none';

              // Check if error message already exists
              const existingError =
                e.target.parentNode.querySelector('.preview-error');
              if (!existingError) {
                // Show error message
                const errorDiv = document.createElement('div');
                errorDiv.className = 'preview-error';
                errorDiv.innerHTML = `
                  <p>Unable to load image preview</p>
                  <p style="font-size: 12px; color: #999; word-break: break-all;">URL: ${mediaUrl}</p>
                  <button onclick="window.open('${mediaUrl}', '_blank')" style="margin-top: 10px;">Open in new tab</button>
                `;
                e.target.parentNode.appendChild(errorDiv);
              }
            }}
            onLoad={() => {
              // Image loaded successfully - no action needed
            }}
          />
        </div>
      );
    }

    // PDF and documents
    if (mimeType?.includes('pdf') || mimeType?.includes('document')) {
      return (
        <div className="preview-content">
          <iframe
            src={mediaUrl}
            title="Document Preview"
            className="preview-iframe"
          />
        </div>
      );
    }

    // Default message for other file types
    return (
      <div className="preview-content">
        <Box className="preview-default">
          <Typography className="body-sm" style={{ marginBottom: '10px' }}>
            Preview not available for this file type.
          </Typography>
          <Typography
            className="body-sm"
            style={{ marginBottom: '15px', color: '#666' }}
          >
            File: {previewMedia?.name || 'Unknown'}
          </Typography>
          <Typography
            className="body-sm"
            style={{ marginBottom: '15px', color: '#666' }}
          >
            Type: {mimeType || 'Unknown'}
          </Typography>
          <button
            onClick={() => window.open(mediaUrl, '_blank')}
            style={{
              padding: '8px 16px',
              backgroundColor: '#007bff',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer',
            }}
          >
            Open in new tab
          </button>
        </Box>
      </div>
    );
  };

  return (
    <Box className="attachment-wrap d-flex flex-col align-center justify-center text-align">
      {loading ? (
        <ContentLoader />
      ) : (
        <>
          {displayFiles?.length === 0 ? (
            <Box className="empty-attachment">
              <EmptAttachmentIcon />
              <Box className="attachment-text-wrap">
                <Typography className="no-attachment body-sm">
                  No Attachments available
                </Typography>
                <Typography className="upload-attachment body-sm">
                  {/* Upload attachments to add more context to this Ticket. */}
                  No attachments found for this ticket.
                </Typography>
              </Box>

              {/* <Box className="browse-files-wrap">
                <CustomButton
                  onClick={open}
                  variant="contained"
                  title="Browse files"
                />
              </Box> */}
            </Box>
          ) : (
            <Box className="media-previews w100">
              {/* <Box className="add-file-wrap d-flex justify-end">
                <CustomButton
                  className="p16 add-file"
                  onClick={open}
                  fontWeight="600"
                  variant="contained"
                  background="#39596e"
                  backgroundhover="#39596e"
                  colorhover="#FFFFFF"
                  title="Browse files"
                />
              </Box> */}
              <FileGrid
                displayFiles={displayFiles}
                onPreviewClick={handlePreviewClick}
                onDownload={handleDownload}
              />
            </Box>
          )}
          {/* <Box {...getRootProps()} style={{ display: 'none' }}>
            <input {...getInputProps()} />
          </Box> */}
          <DialogBox
            open={openModal}
            handleClose={() => setOpenModal(false)}
            title="Attachment Preview"
            className="fullscreen-dialog-box-container attachment-preview-dialog"
            content={getPreviewContent()}
          />
        </>
      )}
    </Box>
  );
}
