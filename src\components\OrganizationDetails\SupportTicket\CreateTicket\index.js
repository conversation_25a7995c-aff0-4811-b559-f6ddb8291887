'use client';
import React, { useState, useEffect } from 'react';
import { Form, Formik } from 'formik';
import * as Yup from 'yup';
import {
  Box,
  Typography,
  useTheme,
  useMediaQuery,
  Paper,
  IconButton,
  Alert,
  Divider,
  InputAdornment,
} from '@mui/material';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import ArrowBackIosIcon from '@mui/icons-material/ArrowBackIos';
import VisibilityOutlinedIcon from '@mui/icons-material/VisibilityOutlined';
import VisibilityOffOutlinedIcon from '@mui/icons-material/VisibilityOffOutlined';
import CustomTextField from '@/components/UI/CustomTextField';
import CustomSelect from '@/components/UI/CustomSelect';
import CustomCheckbox from '@/components/UI/CustomCheckbox';
import CustomButton from '@/components/UI/CustomButton';
import Icon from '@/components/UI/AppIcon/AppIcon';
import AppImage from '@/components/UI/AppImage/AppImage';
import { supportTicketService } from '@/services/supportTicketService';
import { staticOptions } from '@/helper/common/staticOptions';
import {
  setApiMessage,
  checkOrganizationRole,
} from '@/helper/common/commonFunctions';
import { fetchFromStorage, removeFromStorage } from '@/helper/context';
import { identifiers } from '@/helper/constants/identifier';
import { INFO_LINKS } from '@/helper/constants/urls';
import { RecipePlaceholder } from '@/helper/common/recipesImgPlaceholder';
import ContentLoader from '@/components/UI/ContentLoader';
import './createticket.scss';

export default function CreateTicket({ isEdit = false, ticketId = null }) {
  const router = useRouter();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down(1500));

  // Check user role
  const isSuperAdmin = checkOrganizationRole('super_admin');

  // Dynamic validation schema based on user role
  const getValidationSchema = () => {
    const baseSchema = {
      Subject: Yup.string()
        .max(120, 'Subject must not exceed 120 characters')
        .required('Subject is required'),
      category: Yup.string().required('Category is required'),
      issueType: Yup.string().required('Issue Type is required'),
      priority: Yup.string().required('Priority is required'),
      description: Yup.string().required('Description is required'),
      term_condition: Yup.bool().oneOf([true], 'You must agree to the terms'),
    };

    // Add Name, Email, PhoneNumber validation - all fields are optional
    baseSchema.Name = Yup.string();
    baseSchema.Email = Yup.string().email('Invalid email');
    baseSchema.PhoneNumber = Yup.string();

    // Add support pin validation conditionally
    if (isSuperAdmin) {
      // For super admin, support pin is optional
      baseSchema.supportPin = Yup.string()
        .matches(/^\d{4}$/, 'Support Pin must be exactly 4 digits')
        .test(
          'is-four-digits',
          'Support Pin must be exactly 4 digits',
          function (value) {
            if (!value) return true; // Allow empty for optional field
            return /^\d{4}$/.test(value);
          }
        );
    } else {
      // For other users, support pin is required
      baseSchema.supportPin = Yup.string()
        .matches(/^\d{4}$/, 'Support Pin must be exactly 4 digits')
        .required('Support Pin is required');
    }

    return Yup.object(baseSchema);
  };

  const [attachments, setAttachments] = useState([]);
  const [dragOver, setDragOver] = useState(false);
  const [uploadErrors, setUploadErrors] = useState(null);
  const [showSupportPin, setShowSupportPin] = useState(false);
  // TODO: Uncomment if verify pin is required
  // const [showVerifyPin, setShowVerifyPin] = useState(false);
  const [ticketData, setTicketData] = useState(null);
  const [loading, setLoading] = useState(isEdit);

  // Helper function to get proper MIME type from API file_type
  const getMimeTypeFromFileType = (attachment) => {
    if (attachment.file_type === 'image') {
      // Try to get specific image type from file extension
      const fileName = attachment.file_name || '';
      const extension = fileName.split('.').pop()?.toLowerCase();
      switch (extension) {
        case 'png':
          return 'image/png';
        case 'jpg':
        case 'jpeg':
          return 'image/jpeg';
        case 'gif':
          return 'image/gif';
        case 'webp':
          return 'image/webp';
        default:
          return 'image/jpeg'; // Default fallback
      }
    }
    if (attachment.file_type === 'video') {
      const fileName = attachment.file_name || '';
      const extension = fileName.split('.').pop()?.toLowerCase();
      switch (extension) {
        case 'mp4':
          return 'video/mp4';
        case 'mov':
          return 'video/mov';
        case 'avi':
          return 'video/avi';
        case 'webm':
          return 'video/webm';
        default:
          return 'video/mp4'; // Default fallback
      }
    }
    return (
      attachment.mime_type ||
      attachment.item_mime_type ||
      attachment.type ||
      'application/octet-stream'
    );
  };

  // Fetch ticket data when in edit mode
  useEffect(() => {
    const fetchTicketData = async () => {
      if (isEdit && ticketId) {
        try {
          setLoading(true);
          const data = await supportTicketService.getTicketDetails(ticketId);
          setTicketData(data);

          // Load existing attachments if available
          if (
            data?.attachments &&
            Array.isArray(data.attachments) &&
            data.attachments.length > 0
          ) {
            const existingAttachments = data.attachments.map(
              (attachment, index) => ({
                id: `existing_${attachment.id || index}`,
                name:
                  attachment.file_name ||
                  attachment.attachment_name ||
                  attachment.item_name ||
                  attachment.name ||
                  'Unknown file',
                size: attachment.file_size || 0,
                type: getMimeTypeFromFileType(attachment),
                url:
                  attachment.download_url ||
                  attachment.url ||
                  attachment.file_url,
                isExisting: true, // Flag to identify existing files
                attachmentId: attachment.id, // Keep original attachment ID
                originalData: attachment, // Keep original data for reference
              })
            );
            setAttachments(existingAttachments);
          }
        } catch (error) {
          console.error('Error fetching ticket data:', error);
          setApiMessage(
            'error',
            error?.response?.data?.message || 'Failed to fetch ticket data'
          );
          // Navigate back if ticket not found
          router.push('/support-ticket/all-tickets');
        } finally {
          setLoading(false);
        }
      }
    };

    fetchTicketData();
  }, [isEdit, ticketId, router]);

  const handleBackNavigation = () => {
    // Check if there's saved filter data from AllTicketsList
    const redirectData = fetchFromStorage(identifiers?.RedirectData);

    if (redirectData && redirectData?.IsFromUser) {
      // Clear the redirect data since we're going back
      removeFromStorage(identifiers?.RedirectData);
      // Navigate back to all tickets list (filters will be restored automatically)
      router.push('/support-ticket/all-tickets');
    } else {
      // No saved data, navigate normally
      router.push('/support-ticket/all-tickets');
    }
  };

  const handleToggleSupportPinVisibility = () => {
    setShowSupportPin(!showSupportPin);
  };

  const categoryOptions = staticOptions.SUPPORT_TICKET_MODULE_OPTIONS;

  const issueTypeOptions = staticOptions.SUPPORT_TICKET_TYPE_OPTIONS;

  const priorityOptions = staticOptions.SUPPORT_TICKET_PRIORITY_OPTIONS;

  // File validation constraints - Only images and videos allowed
  const FILE_CONSTRAINTS = {
    maxSize: 50 * 1024 * 1024, // 50MB for videos, images will be smaller
    types: [
      // Images
      'image/jpeg',
      'image/png',
      'image/webp',
      'image/gif',
      // Videos
      'video/mp4',
      'video/mov',
      'video/avi',
      'video/webm',
    ],
    maxFiles: 5,
  };

  // Subject text limit
  const SUBJECT_MAX_LENGTH = 120;

  // Get appropriate icon for file type
  const getFileTypeIcon = (file) => {
    const fileType = file?.type?.toLowerCase() || '';

    // Images
    if (fileType.startsWith('image/')) {
      return { name: 'Image', color: '#10B981' };
    }

    // Videos
    if (fileType.startsWith('video/')) {
      return { name: 'Video', color: '#8B5CF6' };
    }

    // Default file icon (shouldn't be reached with current constraints)
    return { name: 'File', color: '#6B7280' };
  };

  // Validate file constraints
  const validateFile = (file) => {
    const errors = [];

    // Check file type
    if (!FILE_CONSTRAINTS.types.includes(file?.type)) {
      errors.push(
        `File type ${file?.type} not supported. Only images and videos are allowed.`
      );
    }

    // Check file size - different limits for images vs videos
    const isImage = file?.type?.startsWith('image/');
    const isVideo = file?.type?.startsWith('video/');

    if (isImage && file?.size > 5 * 1024 * 1024) {
      // 5MB for images
      errors.push('Image size exceeds 5MB limit');
    } else if (isVideo && file?.size > 50 * 1024 * 1024) {
      // 50MB for videos
      errors.push('Video size exceeds 50MB limit');
    }

    return errors;
  };

  const handleFileUpload = (files) => {
    if (!files?.length) return;
    setUploadErrors(null);

    const fileArray = Array.from(files);
    const validFiles = [];
    const fileErrors = [];

    // Check if adding files would exceed limit
    const availableSlots = FILE_CONSTRAINTS.maxFiles - attachments.length;
    if (fileArray.length > availableSlots && availableSlots > 0) {
      fileErrors.push(
        `Cannot add ${fileArray.length} files. Only ${availableSlots} slots available (${attachments.length}/${FILE_CONSTRAINTS.maxFiles} used).`
      );
    } else if (availableSlots <= 0) {
      fileErrors.push(
        `Cannot add more files. Maximum limit reached (${attachments.length}/${FILE_CONSTRAINTS.maxFiles}).`
      );
    }

    // Validate each file
    for (let i = 0; i < fileArray.length; i++) {
      const file = fileArray[i];
      const errors = validateFile(file);
      if (errors.length > 0) {
        fileErrors.push(
          `File ${i + 1} (${file?.name || 'unknown'}): ${errors.join(', ')}`
        );
      } else {
        validFiles.push(file);
      }
    }

    if (fileErrors.length > 0) {
      setUploadErrors(fileErrors);
      return;
    }

    if (validFiles.length > 0) {
      const filesToProcess = validFiles.slice(0, availableSlots);
      const newAttachments = filesToProcess.map((file) => ({
        id: Date.now() + Math.random(),
        name: file.name,
        size: file.size,
        type: file.type,
        url: URL.createObjectURL(file),
        file: file,
      }));

      setAttachments((prev) => [...prev, ...newAttachments]);
    }
  };

  const handleDragOver = (e) => {
    e.preventDefault();
    setDragOver(true);
  };

  const handleDragLeave = (e) => {
    e.preventDefault();
    setDragOver(false);
  };

  const handleDrop = (e) => {
    e.preventDefault();
    setDragOver(false);
    handleFileUpload(e.dataTransfer.files);
  };

  const removeFile = (fileId) => {
    const fileToRemove = attachments.find((file) => file.id === fileId);

    if (fileToRemove?.isExisting) {
      // For existing files, just remove from local state
      // Note: In a full implementation, you might want to track removed files
      // and send a separate API call to delete them from the server
    }

    setAttachments((prev) => prev.filter((file) => file.id !== fileId));
    setUploadErrors(null);
  };

  const handleSubmitForm = async (values, { setSubmitting, resetForm }) => {
    try {
      setSubmitting(true);

      let result;

      if (isEdit) {
        // Prepare update data for edit mode
        const updateData = {
          ticket_title: values.Subject,
          ticket_description: values.description,
          ticket_module: values.category,
          ticket_type: values.issueType,
          ticket_priority: values.priority,
          name: values.Name,
          email: values.Email,
          phone_number: values.PhoneNumber,
        };

        // Only include support_pin if user is not super admin
        if (!isSuperAdmin) {
          updateData.support_pin = values?.supportPin;
        }

        // Separate new files from existing ones
        const newFiles = attachments.filter(
          (attachment) => !attachment.isExisting
        );

        // If there are new files to upload, use the file upload method
        if (newFiles.length > 0) {
          const files = newFiles.map((attachment) => ({
            file: attachment.file,
          }));
          result = await supportTicketService.updateTicketWithFiles(
            ticketId,
            updateData,
            files
          );
        } else {
          // No new files, use regular update method
          result = await supportTicketService.updateTicket(
            ticketId,
            updateData
          );
        }
      } else {
        // Prepare ticket data object for create mode (without verify pin)
        const ticketData = {
          Subject: values?.Subject,
          description: values?.description,
          category: values?.category,
          issueType: values?.issueType,
          priority: values?.priority,
          Name: values?.Name,
          Email: values?.Email,
          PhoneNumber: values?.PhoneNumber,
          // Note: verifyPin is not sent to API as per requirement
        };

        // Only include supportPin if user is not super admin
        if (!isSuperAdmin) {
          ticketData.supportPin = values?.supportPin;
        }

        // Prepare files array for service
        const files = attachments?.map((attachment) => ({
          file: attachment.file,
        }));

        result = await supportTicketService.createTicket(ticketData, files);
      }

      if (result) {
        // Handle the complete API response object
        const successMessage =
          result?.message ||
          (isEdit
            ? 'Ticket updated successfully'
            : 'Ticket created successfully');
        setApiMessage('success', successMessage);

        // Reset form and attachments only for create mode
        if (!isEdit) {
          resetForm();
          setAttachments([]);
          setUploadErrors(null);
        }

        // Check if there's saved filter data and navigate accordingly
        const redirectData = fetchFromStorage(identifiers?.RedirectData);
        if (redirectData && redirectData?.IsFromUser) {
          // Clear the redirect data since we're going back
          removeFromStorage(identifiers?.RedirectData);
          // Navigate back to all tickets list (filters will be restored automatically)
          router.push('/support-ticket/all-tickets');
        } else {
          // No saved data, navigate normally
          router.push('/support-ticket/all-tickets');
        }
      }
    } catch (error) {
      console.error(`Error ${isEdit ? 'updating' : 'creating'} ticket:`, error);
      setApiMessage(
        'error',
        error?.response?.data?.message ||
          `Failed to ${isEdit ? 'update' : 'create'} ticket. Please try again.`
      );
    } finally {
      setSubmitting(false);
    }
  };

  // Show loading state when fetching ticket data in edit mode
  if (loading) {
    return (
      <Box className="section-wrapper">
        <Box className="section-right">
          <Box className="section-right-content">
            <ContentLoader />
          </Box>
        </Box>
      </Box>
    );
  }

  // Show error state if ticket not found in edit mode
  if (isEdit && !ticketData) {
    return (
      <Box className="section-wrapper">
        <Box className="section-right">
          <Box className="section-right-content">
            <Typography>Ticket not found</Typography>
          </Box>
        </Box>
      </Box>
    );
  }

  return (
    <Box className="section-wrapper">
      <Box className="section-right">
        <Box className="section-right-tab-header">
          <Box className="d-flex align-center justify-space-between">
            <Box className="section-right-title d-flex align-center">
              <ArrowBackIosIcon
                className="cursor-pointer"
                onClick={handleBackNavigation}
              />
              <Typography className="sub-header-text">
                {isEdit ? 'Update Ticket' : 'Create New Ticket'}
              </Typography>
            </Box>
          </Box>
          <Divider />
        </Box>
        <Box className="section-right-content">
          {/* --- FORM CONTENT STARTS HERE --- */}
          <Formik
            initialValues={{
              Subject: isEdit ? ticketData?.ticket_title || '' : '',
              category: isEdit ? ticketData?.ticket_module || '' : '',
              issueType: isEdit ? ticketData?.ticket_type || '' : '',
              priority: isEdit ? ticketData?.ticket_priority || '' : '',
              description: isEdit ? ticketData?.ticket_description || '' : '',
              Name: isEdit ? ticketData?.ticket_owner_name || '' : '',
              Email: isEdit ? ticketData?.ticket_owner_email || '' : '',
              PhoneNumber: isEdit ? ticketData?.ticket_owner_phone || '' : '',
              supportPin: '',
              // verifyPin: '',  TODO: Uncomment if verify pin is required
              term_condition: false,
            }}
            validationSchema={getValidationSchema()}
            onSubmit={handleSubmitForm}
          >
            {({
              values,
              errors,
              touched,
              handleBlur,
              handleChange,
              setFieldValue,
              handleSubmit,
              isSubmitting,
            }) => (
              <Form onSubmit={handleSubmit}>
                <Box className="support-ticket-wrap">
                  <Box className="support-ticket-form">
                    <Box
                      className={`ticket-form-first-row ${isMobile ? 'mobile-layout' : ''}`}
                    >
                      <Box className="subject-field-wrap">
                        <Box className="subject-label-row">
                          <Typography variant="body2" className="field-label">
                            Subject
                            <span style={{ color: 'var(--color-danger)' }}>
                              *
                            </span>
                          </Typography>
                          <Typography
                            variant="body2"
                            className="content-text-sm"
                          >
                            {(values.Subject || '').length}/{SUBJECT_MAX_LENGTH}
                          </Typography>
                        </Box>
                        <CustomTextField
                          fullWidth
                          name="Subject"
                          label=""
                          placeholder="Enter subject"
                          value={values.Subject}
                          onChange={handleChange}
                          onBlur={handleBlur}
                          error={touched.Subject && Boolean(errors.Subject)}
                          helperText={touched.Subject && errors.Subject}
                          inputProps={{ maxLength: SUBJECT_MAX_LENGTH }}
                          required
                        />
                      </Box>

                      <Box className="category-issue-fields-wrap">
                        <Box className="category-field-wrap">
                          <CustomSelect
                            label="Category"
                            name="category"
                            placeholder="Select Category"
                            options={categoryOptions}
                            value={
                              categoryOptions?.find(
                                (opt) => opt?.value === values?.category
                              ) || ''
                            }
                            onChange={(selectedOption) =>
                              setFieldValue(
                                'category',
                                selectedOption?.value || ''
                              )
                            }
                            error={touched?.category && errors?.category}
                            helperText={touched?.category && errors?.category}
                            required
                          />
                        </Box>

                        <Box className="issue-type-field-wrap">
                          <CustomSelect
                            label="Issue Type"
                            name="issueType"
                            placeholder="Select Issue Type"
                            options={issueTypeOptions}
                            value={
                              issueTypeOptions?.find(
                                (opt) => opt?.value === values?.issueType
                              ) || ''
                            }
                            onChange={(selectedOption) =>
                              setFieldValue(
                                'issueType',
                                selectedOption?.value || ''
                              )
                            }
                            error={touched?.issueType && errors?.issueType}
                            helperText={touched?.issueType && errors?.issueType}
                            required
                          />
                        </Box>
                      </Box>
                    </Box>

                    {/* Second row: Priority */}
                    <Box
                      className={`ticket-form-second-row ${isMobile ? 'mobile-layout' : ''}`}
                    >
                      <Box className="priority-field-wrap">
                        <CustomSelect
                          label="Priority"
                          name="priority"
                          placeholder="Select Priority"
                          options={priorityOptions}
                          value={
                            priorityOptions?.find(
                              (opt) => opt?.value === values?.priority
                            ) || ''
                          }
                          onChange={(selectedOption) =>
                            setFieldValue(
                              'priority',
                              selectedOption?.value || ''
                            )
                          }
                          error={touched?.priority && errors?.priority}
                          helperText={touched?.priority && errors?.priority}
                          required
                        />
                      </Box>
                    </Box>

                    {/* Description Row - Single Row */}
                    <Box className="description-single-row">
                      <Box className="description-field-wrap">
                        <CustomTextField
                          fullWidth
                          name="description"
                          label="Description"
                          placeholder="Enter description..."
                          value={values?.description}
                          onBlur={handleBlur}
                          onChange={handleChange}
                          error={Boolean(
                            touched?.description && errors?.description
                          )}
                          helperText={
                            touched?.description && errors?.description
                          }
                          multiline
                          maxRows={4}
                          minRows={3}
                          required
                        />
                      </Box>
                    </Box>

                    {/* Attachment Row - Single Row */}
                    <Box className="attachment-single-row">
                      <Box className="attachment-field-wrap">
                        <Box className="ticket-upload-zone">
                          <Box className="ticket-zone-header">
                            <Typography
                              variant="h6"
                              className="body-sm attachment-label"
                            >
                              Attachments
                            </Typography>
                            <Typography
                              variant="body2"
                              className="content-text-sm"
                            >
                              {attachments.length}/{FILE_CONSTRAINTS.maxFiles}{' '}
                              files
                            </Typography>
                          </Box>

                          <Paper
                            onDragOver={handleDragOver}
                            onDragLeave={handleDragLeave}
                            onDrop={handleDrop}
                            className={`ticket-drop-area ${
                              dragOver ? 'ticket-drop-area--drag-over' : ''
                            } ${
                              attachments.length >= FILE_CONSTRAINTS.maxFiles
                                ? 'ticket-drop-area--disabled'
                                : ''
                            }`}
                            elevation={0}
                            component="label"
                            sx={{ cursor: 'pointer' }}
                          >
                            <input
                              type="file"
                              multiple={true}
                              accept="image/*,video/*"
                              onChange={(e) => handleFileUpload(e.target.files)}
                              className="ticket-drop-input"
                              disabled={
                                attachments.length >= FILE_CONSTRAINTS.maxFiles
                              }
                              style={{ display: 'none' }}
                            />

                            <Box className="ticket-drop-content">
                              <Icon
                                name="Upload"
                                size={32}
                                className="ticket-drop-icon"
                              />
                              <Box className="ticket-drop-text">
                                <Typography
                                  variant="body1"
                                  className="body-sm ticket-drop-placeholder-primary"
                                >
                                  {dragOver
                                    ? 'Drop files here'
                                    : 'Drag & drop files or click to browse'}
                                </Typography>
                                <Typography
                                  variant="body2"
                                  className="content-text-sm"
                                >
                                  Images up to 5MB, Videos up to 50MB each
                                </Typography>
                              </Box>
                            </Box>
                          </Paper>

                          {/* File Preview Grid - Exact Reference Structure */}
                          {attachments.length > 0 && (
                            <div className="media-upload-section__preview-grid">
                              {attachments?.map?.((file) => (
                                <div
                                  key={file?.id}
                                  className="media-upload-section__preview-item"
                                >
                                  <div className="media-upload-section__preview-container">
                                    {file?.type?.includes?.('image') ||
                                    (file?.isExisting &&
                                      file?.originalData?.file_type ===
                                        'image') ? (
                                      <AppImage
                                        src={file?.url}
                                        alt={file?.name || 'Preview'}
                                        className="media-upload-section__preview-image"
                                        isSvg={!file?.isExisting} // Only use SVG placeholder for new files
                                        SvgImg={RecipePlaceholder}
                                      />
                                    ) : (
                                      <div className="media-upload-section__preview-placeholder">
                                        {(() => {
                                          const iconData =
                                            getFileTypeIcon(file);
                                          return (
                                            <Icon
                                              name={iconData.name}
                                              size={24}
                                              color={iconData.color}
                                            />
                                          );
                                        })()}
                                      </div>
                                    )}
                                  </div>

                                  <Icon
                                    name="X"
                                    size={16}
                                    className="media-upload-section__preview-remove"
                                    onClick={() => removeFile(file?.id)}
                                  />

                                  <div className="media-upload-section__preview-info">
                                    <p
                                      className="media-upload-section__preview-name"
                                      title={file?.name}
                                    >
                                      {file?.name || 'Unknown file'}
                                    </p>
                                  </div>
                                </div>
                              )) || []}
                            </div>
                          )}

                          {/* Error Display */}
                          {uploadErrors && (
                            <Alert
                              severity="error"
                              className="ticket-upload-error"
                              icon={<Icon name="AlertCircle" size={16} />}
                            >
                              <Typography
                                variant="body2"
                                className="other-field-error-text"
                              >
                                {Array.isArray(uploadErrors)
                                  ? uploadErrors.join(', ')
                                  : uploadErrors}
                              </Typography>
                            </Alert>
                          )}
                        </Box>
                      </Box>
                    </Box>

                    {/* Personal Information Section */}
                    <Box className="personal-info-wrap">
                      <Typography className="title-text" component="p">
                        Please provide your personal details for further
                        communication.
                      </Typography>
                      {/* First row: Name, Email, Phone Number */}
                      <Box
                        className={`personal-info-grid-container ${isMobile ? 'mobile-layout' : ''}`}
                      >
                        <Box>
                          <CustomTextField
                            fullWidth
                            name="Name"
                            label="Name"
                            placeholder="Enter your name"
                            value={values.Name}
                            onChange={handleChange}
                            onBlur={handleBlur}
                            error={touched.Name && Boolean(errors.Name)}
                            helperText={touched.Name && errors.Name}
                          />
                        </Box>

                        <Box>
                          <CustomTextField
                            fullWidth
                            name="Email"
                            label="Email"
                            placeholder="Enter your email"
                            value={values.Email}
                            onChange={handleChange}
                            onBlur={handleBlur}
                            error={touched.Email && Boolean(errors.Email)}
                            helperText={touched.Email && errors.Email}
                          />
                        </Box>

                        <Box>
                          <CustomTextField
                            fullWidth
                            name="PhoneNumber"
                            label="Phone Number"
                            placeholder="Enter your phone number"
                            value={values.PhoneNumber}
                            onChange={(e) => {
                              // Only allow numeric input
                              const value = e.target.value.replace(/\D/g, '');
                              setFieldValue('PhoneNumber', value);
                            }}
                            onBlur={handleBlur}
                            error={
                              touched.PhoneNumber && Boolean(errors.PhoneNumber)
                            }
                            helperText={
                              touched.PhoneNumber && errors.PhoneNumber
                            }
                            inputProps={{
                              pattern: '[0-9]*',
                              inputMode: 'numeric',
                            }}
                          />
                        </Box>
                      </Box>

                      {/* Second row: Support Pin and Verify Pin */}
                      <Box
                        className={`pin-fields-grid-container ${isMobile ? 'mobile-layout' : ''}`}
                      >
                        <Box>
                          <CustomTextField
                            fullWidth
                            name="supportPin"
                            label="Support Pin"
                            placeholder="Enter 4-digit support pin"
                            type={showSupportPin ? 'text' : 'password'}
                            value={values.supportPin}
                            onChange={(e) => {
                              // Only allow numeric input and limit to 4 digits
                              const value = e.target.value
                                .replace(/\D/g, '')
                                .slice(0, 4);
                              setFieldValue('supportPin', value);
                            }}
                            onBlur={handleBlur}
                            error={
                              touched.supportPin && Boolean(errors.supportPin)
                            }
                            helperText={touched.supportPin && errors.supportPin}
                            required={!isSuperAdmin} // Non-mandatory for super admin
                            inputProps={{
                              maxLength: 4,
                              pattern: '[0-9]*',
                              inputMode: 'numeric',
                            }}
                            InputProps={{
                              endAdornment: (
                                <InputAdornment position="end">
                                  <IconButton
                                    onClick={handleToggleSupportPinVisibility}
                                    edge="end"
                                  >
                                    {showSupportPin ? (
                                      <VisibilityOffOutlinedIcon />
                                    ) : (
                                      <VisibilityOutlinedIcon />
                                    )}
                                  </IconButton>
                                </InputAdornment>
                              ),
                            }}
                          />
                        </Box>
                        {/* TODO: Uncomment if verify pin is required */}
                        {/* <Box>
                          <CustomTextField
                            fullWidth
                            name="verifyPin"
                            label="Verify Pin"
                            placeholder="Re-enter support pin"
                            type={showVerifyPin ? 'text' : 'password'}
                            value={values.verifyPin}
                            onChange={handleChange}
                            onBlur={handleBlur}
                            error={
                              touched.verifyPin && Boolean(errors.verifyPin)
                            }
                            helperText={touched.verifyPin && errors.verifyPin}
                            required
                            InputProps={{
                              endAdornment: (
                                <InputAdornment position="end">
                                  <IconButton
                                    onClick={handleToggleVerifyPinVisibility}
                                    edge="end"
                                  >
                                    {showVerifyPin ? (
                                      <VisibilityOffOutlinedIcon />
                                    ) : (
                                      <VisibilityOutlinedIcon />
                                    )}
                                  </IconButton>
                                </InputAdornment>
                              ),
                            }}
                          />
                        </Box> */}{' '}
                      </Box>
                    </Box>

                    {/* Terms and Conditions */}
                    <Box className="pt16">
                      <CustomCheckbox
                        checked={values?.term_condition}
                        onChange={handleChange}
                        name="term_condition"
                        label={
                          <Typography
                            component="span"
                            className="terms-condition-label"
                          >
                            I agree to the
                            <Link
                              href={INFO_LINKS?.TermsConditions}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="terms-link"
                            >
                              Terms and Conditions
                            </Link>
                            <span style={{ color: 'var(--color-danger)' }}>
                              *
                            </span>
                            .
                          </Typography>
                        }
                      />
                      {touched?.term_condition && errors?.term_condition && (
                        <Typography
                          variant="body2"
                          className="other-field-error-text"
                        >
                          {errors?.term_condition}
                        </Typography>
                      )}
                    </Box>

                    {/* Submit Button */}
                    <Box className="pt24">
                      <Box className="d-flex justify-end gap-sm">
                        <CustomButton
                          type="button"
                          title="Cancel"
                          variant="outlined"
                          onClick={handleBackNavigation}
                        />
                        <CustomButton
                          type="submit"
                          title={
                            isSubmitting
                              ? isEdit
                                ? 'Updating...'
                                : 'Creating...'
                              : isEdit
                                ? 'Update Ticket'
                                : 'Create Ticket'
                          }
                          variant="contained"
                          disabled={
                            isSubmitting ||
                            !values.Subject ||
                            !values.category ||
                            !values.issueType ||
                            !values.priority ||
                            !values.description ||
                            (!isSuperAdmin && !values.supportPin) || // Only require supportPin for non-super admins
                            !values.term_condition ||
                            Boolean(errors.Subject) ||
                            Boolean(errors.category) ||
                            Boolean(errors.issueType) ||
                            Boolean(errors.priority) ||
                            Boolean(errors.description) ||
                            Boolean(errors.supportPin) ||
                            Boolean(errors.term_condition)
                          }
                        />
                      </Box>
                    </Box>
                  </Box>
                </Box>
              </Form>
            )}
          </Formik>
          {/* --- FORM CONTENT ENDS HERE --- */}
        </Box>
      </Box>
    </Box>
  );
}
