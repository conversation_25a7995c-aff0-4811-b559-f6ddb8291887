'use client';
import React, { useState } from 'react';
import PropTypes from 'prop-types';
import CustomDateRangePicker from '../CustomDateRangePicker';
import CustomSelect from '../CustomSelect';
import CustomTextField from '../CustomTextField';
import CustomButton from '../CustomButton';
import './FilterCollapse.scss';
import { Accordion, AccordionSummary, AccordionDetails } from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import FilterListIcon from '@mui/icons-material/FilterList';

const FilterCollapse = ({
  fields = [],
  onApply,
  buttonText = 'Apply Filters',
  initialValues = {},
  children,
}) => {
  const [expanded, setExpanded] = useState(false);
  const [values, setValues] = useState(initialValues);

  const handleFieldChange = (name, value) => {
    setValues((prev) => ({ ...prev, [name]: value }));
  };

  const handleApply = () => {
    if (onApply) onApply(values);
  };

  return (
    <div className="filter-collapse-container">
      <Accordion
        expanded={expanded}
        onChange={() => setExpanded((prev) => !prev)}
        className="filter-collapse-accordion"
        elevation={0}
      >
        <AccordionSummary
          className="filter-collapse-header"
          expandIcon={<ExpandMoreIcon />}
        >
          <span className="filter-collapse-icon">
            <FilterListIcon />
          </span>
          <span className="filter-collapse-title">Filters</span>
        </AccordionSummary>
        <AccordionDetails className="filter-collapse-accordion-details">
          <div className="filter-collapse-fields">
            <div className="filter-fields-row">
              {fields.map((field) => {
                if (field.type === 'date-range') {
                  return (
                    <div className="filter-field" key={field.name}>
                      <CustomDateRangePicker
                        label={field.label}
                        value={values[field.name] || [null, null]}
                        onChange={(range) =>
                          handleFieldChange(field.name, range)
                        }
                        placeholder={field.placeholder}
                        disabled={field.disabled}
                        format={field.format}
                      />
                    </div>
                  );
                }
                if (field.type === 'select') {
                  return (
                    <div className="filter-field" key={field.name}>
                      <label className="field-label">{field.label}</label>
                      <CustomSelect
                        placeholder={
                          field.placeholder || `Select ${field.label}`
                        }
                        options={field.options || []}
                        value={
                          field.options?.find(
                            (opt) => opt.value === values[field.name]
                          ) || ''
                        }
                        onChange={(opt) =>
                          handleFieldChange(field.name, opt?.value)
                        }
                      />
                    </div>
                  );
                }
                if (field.type === 'text') {
                  return (
                    <div className="filter-field" key={field.name}>
                      <label className="field-label">{field.label}</label>
                      <CustomTextField
                        placeholder={field.placeholder || field.label}
                        value={values[field.name] || ''}
                        onChange={(e) =>
                          handleFieldChange(field.name, e.target.value)
                        }
                      />
                    </div>
                  );
                }
                return null;
              })}
            </div>
            {children}
            <div className="filter-collapse-action-row">
              <CustomButton
                title={buttonText}
                variant="contained"
                onClick={handleApply}
              />
            </div>
          </div>
        </AccordionDetails>
      </Accordion>
    </div>
  );
};

FilterCollapse.propTypes = {
  fields: PropTypes.arrayOf(
    PropTypes.shape({
      type: PropTypes.oneOf(['date-range', 'select', 'text']).isRequired,
      label: PropTypes.string.isRequired,
      name: PropTypes.string.isRequired,
      options: PropTypes.array,
      placeholder: PropTypes.string,
    })
  ),
  onApply: PropTypes.func,
  buttonText: PropTypes.string,
  initialValues: PropTypes.object,
  children: PropTypes.node,
};

export default FilterCollapse;
