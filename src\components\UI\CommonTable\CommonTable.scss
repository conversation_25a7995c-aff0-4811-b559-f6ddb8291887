body {
  .common-table-container {
    width: 100%;
    border-radius: var(--border-radius-md);
    background-color: var(--color-white);
    border-radius: var(--border-radius-md);
    box-shadow: var(--box-shadow-xs);
    border: var(--normal-sec-border);
    .MuiTableContainer-root {
      border-radius: var(--border-radius-sm);
    }
    .MuiTableRow-root.common-table-row:hover {
      background-color: var(--color-primary-opacity);
    }
    .MuiTableCell-root {
      font-family: var(--font-family-primary);
      word-break: break-word;
    }
    .MuiTableCell-head {
      font-weight: bold;
      background: var(--color-primary);
      color: var(--text-color-white);
    }
    .MuiTableSortLabel-root.Mui-active {
      color: var(--color-primary);
    }
    .common-table-pagination {
      margin: var(--spacing-none) var(--spacing-md);
      padding-bottom: var(--spacing-md);
    }
  }
}
